import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // Performance optimizations for Next.js 15
  experimental: {
    // Configure client-side router cache stale times for better performance
    staleTimes: {
      dynamic: 30, // 30 seconds for dynamic routes
      static: 180, // 3 minutes for static routes
    },
    // Optimize package imports to reduce bundle size
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      '@clerk/nextjs',
      '@clerk/themes',
      'framer-motion',
      'date-fns',
    ],
  },

  // Transpile better-auth-harmony for ESM compatibility
  transpilePackages: ['better-auth-harmony'],

  // Image optimization
  images: {
    // Use remotePatterns for better security and flexibility
    remotePatterns: [
      // Allow all HTTPS images (most secure approach for external images)
      {
        protocol: 'https',
        hostname: '**',
      },
      // Allow HTTP for localhost development
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '127.0.0.1',
      },
    ],
    // Enable modern image formats for better compression
    formats: ['image/webp', 'image/avif'],
    // Optimize image sizes for different devices
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Cache optimized images longer
    minimumCacheTTL: 86400, // 24 hours
    // Allow data URLs for base64 images
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Loader configuration for better performance
    loader: 'default',
    // Disable static imports for better tree shaking
    unoptimized: false,
  },

  // Compiler optimizations
  compiler: {
    // Remove console logs in production
    // TODO: look at different option to remove only frontend logs
    // removeConsole: process.env.NODE_ENV === 'production',
  },

  // Performance and caching
  poweredByHeader: false,
  compress: true,

  // PostHog rewrites
  async rewrites() {
    return [
      {
        source: '/ingest/static/:path*',
        destination: 'https://us-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/ingest/:path*',
        destination: 'https://us.i.posthog.com/:path*',
      },
      {
        source: '/ingest/decide',
        destination: 'https://us.i.posthog.com/decide',
      },
    ]
  },

  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,

  // Fix: Ignore .md files (e.g. @esbuild/darwin-arm64/README.md) to prevent build errors with Remotion/Next.js
  webpack(config, { dev, isServer }) {
    config.module.rules.push(
      {
        test: /\.md$/,
        use: 'ignore-loader',
      },
      {
        test: /\.d\.ts$/,
        use: 'ignore-loader',
      }
    )

    // Increase memory limit for build process to handle large dependency graphs
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        // Reduce memory usage during build
        moduleIds: 'deterministic',
        chunkIds: 'deterministic',
      }
    }

    // Optimize bundle splitting in production
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        // Reduce memory usage by limiting chunk analysis
        maxAsyncRequests: 10,
        maxInitialRequests: 6,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          // Separate vendor chunks for better caching
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
            maxSize: 500000, // Limit chunk size to reduce memory usage
          },
          // Separate UI components chunk
          ui: {
            test: /[\\/]src[\\/]components[\\/]ui[\\/]/,
            name: 'ui',
            chunks: 'all',
            priority: 20,
            maxSize: 300000,
          },
          // Separate Remotion components to reduce memory pressure
          remotion: {
            test: /[\\/]src[\\/]lib[\\/]remotion[\\/]/,
            name: 'remotion',
            chunks: 'all',
            priority: 15,
            maxSize: 400000,
          },
        },
      }
    }

    return config
  },
}

export default nextConfig
