{"name": "adori-ai-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "drizzle-kit generate", "db:migrate": "NODE_TLS_REJECT_UNAUTHORIZED='0' drizzle-kit migrate", "db:push": " NODE_TLS_REJECT_UNAUTHORIZED='0' drizzle-kit push", "db:studio": "NODE_TLS_REJECT_UNAUTHORIZED='0' drizzle-kit studio", "inngest:dev": "inngest dev", "remotion:render": "remotion render remotion/index.tsx main --props=props/props.json --out=out/test-local.mp4", "remotion:preview": "remotion preview remotion/index.tsx", "remotion:cloudrun-delete": "npx remotion cloudrun services rmall -y", "remotion:cloudrun-deploy": "npx remotion cloudrun services deploy --cpuLimit=4 --memoryLimit=8Gi --maxInstances=100 --timeoutSeconds=3600 --region=us-central1", "remotion:cloudrun-site": "npx remotion cloudrun sites create remotion/index.tsx --site-name=remotion-renderer-v1", "remotion:cloudrun-render": "npx remotion cloudrun render remotion-renderer-v1 main --props=props/props.json --cloud-run-url=https://remotion-4-0-323-mem8gi-cpu4-t3600-4n2h7bqyca-ue.a.run.app", "remotion:aws-render": "npx remotion lambda render remotion-aws-site main --props=props/props.json  --overwrite --concurrency=200 --log=verbose", "update:gcp-cors": "gsutil cors set cors.json gs://remotioncloudrun-iexx606ijk"}, "dependencies": {"@aws-sdk/client-ses": "^3.855.0", "@better-auth/stripe": "^1.3.4", "@hookform/resolvers": "^5.1.1", "@lexical/code": "^0.32.1", "@lexical/file": "^0.32.1", "@lexical/history": "^0.32.1", "@lexical/link": "^0.32.1", "@lexical/list": "^0.32.1", "@lexical/mark": "^0.32.1", "@lexical/markdown": "^0.32.1", "@lexical/plain-text": "^0.32.1", "@lexical/react": "^0.32.1", "@lexical/rich-text": "^0.32.1", "@lexical/selection": "^0.32.1", "@lexical/table": "^0.32.1", "@lexical/text": "^0.32.1", "@lexical/utils": "^0.32.1", "@mozilla/readability": "^0.6.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@remotion/bundler": "4.0.323", "@remotion/cli": "4.0.323", "@remotion/cloudrun": "4.0.323", "@remotion/core": "1.0.0-y.46", "@remotion/lambda": "4.0.323", "@remotion/player": "4.0.323", "@remotion/preload": "4.0.323", "@remotion/renderer": "4.0.323", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.76.2", "@types/axios": "^0.14.4", "@types/pg": "^8.15.4", "@udecode/plate-tag": "^48.0.0", "axios": "^1.10.0", "better-auth": "^1.3.4", "better-auth-harmony": "^1.2.5", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "ffprobe": "^1.1.2", "ffprobe-static": "^3.1.0", "flag-icons": "^7.5.0", "form-data": "^4.0.3", "framer-motion": "^12.18.1", "google-auth-library": "^10.1.0", "googleapis": "^154.0.0", "inngest": "^3.39.2", "jsdom": "^26.1.0", "lexical": "^0.32.1", "lucide-react": "^0.518.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "object-hash": "^3.0.0", "openai": "^5.10.1", "pg": "^8.16.0", "postgres": "^3.4.7", "posthog-js": "^1.257.1", "posthog-node": "^5.5.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "remotion": "4.0.323", "sonner": "^2.0.5", "stripe": "^18.4.0", "tailwind-merge": "^3.3.0", "turndown": "^7.2.0", "unpdf": "^1.0.6", "validator": "^13.15.15", "vaul": "^1.1.2", "webfontloader": "^1.6.28", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bun": "^1.2.19", "@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "@types/turndown": "^5.0.5", "@types/webfontloader": "^1.6.38", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "inngest-cli": "^1.8.0", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}