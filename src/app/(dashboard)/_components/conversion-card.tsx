import React from 'react'
import Link from 'next/link'
import { CardDescription, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  CardWithHover,
  CardWithHoverHeader,
  CardWithHoverFooter,
} from '@/components/ui/extended'

interface ConversionCardProps {
  title: string
  description: string
  icon: React.ElementType
  actionText: string
  href: string
}

// Visual distinction through different icon background styles using Shadcn UI variables
const iconStyleMap: Record<
  string,
  {
    background: string
    iconColor: string
    borderStyle?: string
    shadowStyle?: string
  }
> = {
  'Idea to Video': {
    background: 'bg-primary/90',
    iconColor: 'text-primary-foreground',
    borderStyle: 'ring-2 ring-primary/20',
    shadowStyle: 'shadow-lg shadow-primary/25',
  },
  'Blog to Video': {
    background: 'bg-gradient-to-br from-primary to-primary/70',
    iconColor: 'text-primary-foreground',
    borderStyle: 'ring-2 ring-primary/30',
    shadowStyle: 'shadow-lg shadow-primary/30',
  },
  'Text to Video': {
    background: 'bg-secondary',
    iconColor: 'text-secondary-foreground',
    borderStyle:
      'ring-2 ring-secondary/50 ring-offset-2 ring-offset-background',
    shadowStyle: 'shadow-lg shadow-secondary/40',
  },
  'PDF to Video': {
    background: 'bg-accent',
    iconColor: 'text-accent-foreground',
    borderStyle: 'ring-2 ring-accent/50',
    shadowStyle: 'shadow-lg shadow-accent/40',
  },
  'Audio to Video': {
    background: 'bg-gradient-to-br from-secondary to-accent',
    iconColor: 'text-secondary-foreground',
    borderStyle: 'ring-2 ring-secondary/40',
    shadowStyle: 'shadow-lg shadow-secondary/35',
  },
  'Podcast to Video': {
    background: 'bg-muted',
    iconColor: 'text-muted-foreground',
    borderStyle: 'ring-2 ring-muted-foreground/20',
    shadowStyle: 'shadow-lg shadow-muted/50',
  },
}

const ConversionCard = ({
  title,
  description,
  icon: Icon,
  actionText,
  href,
}: ConversionCardProps) => {
  const iconStyle = iconStyleMap[title] || iconStyleMap['Idea to Video']

  return (
    <CardWithHover className='bg-card/60 border-border/60 hover:bg-card/80 hover:border-border hover:shadow-md transition-all duration-300 ease-out backdrop-blur-sm group relative overflow-hidden'>
      {/* Subtle background pattern for visual interest */}
      <div className='absolute inset-0 bg-gradient-to-br from-primary/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300' />

      <CardWithHoverHeader className='relative flex flex-col items-center justify-center pt-6 pb-3 px-6'>
        <div className='flex items-center justify-center mb-3'>
          <div
            className={`
              rounded-2xl
              ${iconStyle.background}
              ${iconStyle.iconColor}
              ${iconStyle.borderStyle || ''}
              ${iconStyle.shadowStyle || 'shadow-lg'}
              flex items-center justify-center
              w-14 h-14
              transition-all duration-300
              group-hover:shadow-xl
              group-hover:scale-110
              group-hover:-translate-y-1
              relative
            `}
          >
            <Icon className='h-7 w-7 transition-transform duration-300 group-hover:scale-110' />
          </div>
        </div>

        <div className='space-y-1.5 text-center'>
          <CardTitle className='text-base font-semibold leading-tight text-center px-2 group-hover:text-primary transition-colors duration-300'>
            {title}
          </CardTitle>
          <CardDescription className='text-sm text-muted-foreground/80 leading-snug text-center px-2 min-h-[2rem] flex items-center justify-center'>
            {description}
          </CardDescription>
        </div>
      </CardWithHoverHeader>

      <CardWithHoverFooter className='relative px-6 pb-5 pt-1 flex justify-center'>
        <Button
          asChild
          variant='default'
          className='w-full max-w-[160px] h-11 font-medium transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105 hover:-translate-y-0.5'
        >
          <Link href={href}>{actionText}</Link>
        </Button>
      </CardWithHoverFooter>
    </CardWithHover>
  )
}

export default ConversionCard
