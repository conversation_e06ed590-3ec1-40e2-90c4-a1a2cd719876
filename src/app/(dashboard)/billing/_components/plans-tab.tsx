'use client'
import React from 'react'
import { ArrowRight } from 'lucide-react'
import { BillingToggle, PricingTable, type Plan } from './index'

// Plan data - using constants configuration
const plans: Plan[] = [
  {
    name: 'Free',
    price: '$0',
    billingPeriod: '/forever',
    description: 'For individuals',
    highlightTag: null,
    videoCreation: 3, // 3 projects
    videoDownloads: 0, // 0 exports
    maxVideoLength: '1 minute',
    resolution: 'HD',
    aiImageGenerator: '20 images',
    publishToYoutube: false,
    proVoices: false,
    voiceCloning: false,
    curationSupport: false,
    buttonText: 'Get started',
    buttonVariant: 'outline' as const,
  },
  {
    name: 'Basic',
    price: '$15',
    annualPrice: '$144',
    annualSavings: 'Save $36',
    billingPeriod: 'billed annually',
    description: 'For small to medium-sized businesses',
    highlightTag: null,
    videoCreation: 10, // 10 projects
    videoDownloads: 10, // 10 exports
    maxVideoLength: '3 minutes',
    resolution: 'HD',
    aiImageGenerator: '100 images',
    publishToYoutube: false,
    proVoices: false,
    voiceCloning: false,
    curationSupport: false,
    buttonText: 'Get started',
    buttonVariant: 'default' as const,
  },
  {
    name: 'Premium',
    price: '$45',
    annualPrice: '$432',
    annualSavings: 'Save $108',
    billingPeriod: 'billed annually',
    description: 'For growing businesses and teams',
    highlightTag: 'Most Popular',
    videoCreation: 20, // 20 projects
    videoDownloads: 20, // 20 exports
    maxVideoLength: '5 minutes',
    resolution: 'Full HD',
    aiImageGenerator: '200 images',
    publishToYoutube: true,
    proVoices: true,
    voiceCloning: false,
    curationSupport: false,
    buttonText: 'Get started',
    buttonVariant: 'default' as const,
  },
]

export function PlansTab() {
  const [billingPeriod, setBillingPeriod] = React.useState<
    'monthly' | 'annual'
  >('annual')

  return (
    <div className='space-y-8'>
      {/* Page header */}
      <div>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8'>
          <div className='text-center sm:text-left mb-6 sm:mb-0'>
            <h2 className='text-2xl font-bold mb-3'>Choose your plan</h2>
          </div>
        </div>
      </div>

      {/* Pricing Table */}
      <div>
        {/* Billing Toggle */}
        <div className='flex justify-center mb-4'>
          <BillingToggle
            activePeriod={billingPeriod}
            onChange={setBillingPeriod}
          />
        </div>
        <PricingTable plans={plans} billingPeriod={billingPeriod} />
      </div>

      {/* Enterprise Contact Section */}
      <div className='w-full mx-auto rounded-lg bg-card border border-border/40 p-8 text-center'>
        <div className='max-w-lg mx-auto'>
          <h3 className='text-xl font-semibold mb-3'>
            Need a custom enterprise solution?
          </h3>
          <p className='text-muted-foreground mb-6 text-sm'>
            Our team can help you customize a plan that meets your specific
            requirements.
          </p>
          <a
            href='mailto:<EMAIL>'
            target='_blank'
            className='inline-flex items-center gap-1.5 text-primary hover:text-primary/90 font-medium'
          >
            Contact our sales team
            <ArrowRight className='h-4 w-4' />
          </a>
        </div>
      </div>
    </div>
  )
}
