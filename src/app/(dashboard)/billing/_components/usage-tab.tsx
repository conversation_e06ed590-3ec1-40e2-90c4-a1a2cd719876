'use client'

import { useUsage } from '@/hooks/use-usage'
import { useCachedActiveSubscription } from '@/hooks/useSubscription'
import { plans } from '@/config/constants'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  FolderOpen,
  Video,
  Image,
  Users,
  HardDrive,
  Calendar,
  LucideIcon,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

const UsageCard = ({
  title,
  icon: Icon,
  used,
  max,
  percentage,
  color = 'blue',
}: {
  title: string
  icon: LucideIcon
  used: number
  max: number
  percentage: number
  color?: string
}) => {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-500'
      case 'green':
        return 'bg-green-500'
      case 'purple':
        return 'bg-purple-500'
      case 'orange':
        return 'bg-orange-500'
      case 'red':
        return 'bg-red-500'
      default:
        return 'bg-blue-500'
    }
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-orange-500'
    return 'bg-green-500'
  }

  return (
    <Card className='hover:shadow-md transition-all duration-300'>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <div className={`p-2 rounded-lg ${getColorClasses(color)}`}>
              <Icon className='h-5 w-5 text-white' />
            </div>
            <div>
              <CardTitle className='text-sm font-medium'>{title}</CardTitle>
              <p className='text-xs text-muted-foreground'>
                {used} of {max === -1 ? '∞' : max} used
              </p>
            </div>
          </div>
          <Badge
            variant={
              percentage >= 90
                ? 'destructive'
                : percentage >= 75
                  ? 'secondary'
                  : 'default'
            }
            className='text-xs'
          >
            {percentage}%
          </Badge>
        </div>
      </CardHeader>
      <CardContent className='pt-0'>
        <div className='relative h-2 w-full overflow-hidden rounded-full bg-primary/20'>
          <div
            className={`h-full transition-all ${getProgressColor(percentage)}`}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export function UsageTab() {
  const { data: usageData, isLoading: usageLoading, error } = useUsage()
  const { subscription, isLoading: subscriptionLoading } =
    useCachedActiveSubscription()
  const isLoading = usageLoading || subscriptionLoading

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <div className='space-y-2'>
          <h2 className='text-2xl font-bold'>Usage</h2>
          <p className='text-muted-foreground'>Track your current plan usage</p>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {[...Array(5)].map((_, i) => (
            <Card key={i} className='animate-pulse'>
              <CardHeader className='pb-3'>
                <div className='flex items-center space-x-3'>
                  <div className='w-10 h-10 bg-muted rounded-lg' />
                  <div className='space-y-2'>
                    <div className='h-4 bg-muted rounded w-20' />
                    <div className='h-3 bg-muted rounded w-16' />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className='h-2 bg-muted rounded' />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className='space-y-6'>
        <div className='space-y-2'>
          <h2 className='text-2xl font-bold'>Usage</h2>
          <p className='text-muted-foreground'>Failed to load usage data</p>
        </div>
        <div className='text-center space-y-4'>
          <p className='text-muted-foreground'>Failed to load usage data</p>
          <button
            onClick={() => window.location.reload()}
            className='px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90'
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (!usageData) {
    return null
  }

  // Determine the actual plan type and limits
  const planType =
    subscription?.plan?.toLowerCase() ||
    usageData.plan?.type?.toLowerCase() ||
    'free'
  const plan = plans.find((p: { name: string }) => p.name === planType)
  const limits = plan?.limits || plans[0].limits // Default to free plan

  const usageItems = [
    {
      title: 'Projects',
      icon: FolderOpen,
      used: usageData.usage.projects.used,
      max: limits.projects,
      percentage: Math.round(
        (usageData.usage.projects.used / limits.projects) * 100
      ),
      color: 'blue' as const,
    },
    {
      title: 'Video Exports',
      icon: Video,
      used: usageData.usage.videoExports.used,
      max: limits.videoExports,
      percentage: Math.round(
        (usageData.usage.videoExports.used / limits.videoExports) * 100
      ),
      color: 'green' as const,
    },
    {
      title: 'AI Images',
      icon: Image,
      used: usageData.usage.aiImages.used,
      max: limits.aiImages,
      percentage: Math.round(
        (usageData.usage.aiImages.used / limits.aiImages) * 100
      ),
      color: 'purple' as const,
    },
    {
      title: 'Team Members',
      icon: Users,
      used: usageData.usage.teamMembers.used,
      max: limits.teamMembers,
      percentage: Math.round(
        (usageData.usage.teamMembers.used / limits.teamMembers) * 100
      ),
      color: 'orange' as const,
    },
    {
      title: 'Storage',
      icon: HardDrive,
      used: usageData.usage.storage.used,
      max: limits.storage,
      percentage: Math.round(
        (usageData.usage.storage.used / limits.storage) * 100
      ),
      color: 'red' as const,
    },
  ]

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='space-y-2'>
        <div className='flex items-center justify-between'>
          <div>
            <h2 className='text-2xl font-bold'>Usage</h2>
            <p className='text-muted-foreground'>
              Track your current plan usage
            </p>
          </div>
          <div className='flex items-center space-x-2'>
            <Badge variant='outline' className='capitalize'>
              {planType === 'free'
                ? 'Free'
                : planType === 'basic'
                  ? 'Basic'
                  : planType === 'premium'
                    ? 'Premium'
                    : planType}{' '}
              Plan
            </Badge>
            <Badge
              variant={
                usageData.plan.status === 'active' ? 'default' : 'secondary'
              }
            >
              {usageData.plan.status}
            </Badge>
          </div>
        </div>

        {/* Plan Period Info */}
        <div className='flex items-center space-x-4 text-sm text-muted-foreground'>
          <div className='flex items-center space-x-1'>
            <Calendar className='h-4 w-4' />
            <span>
              Period:{' '}
              {formatDistanceToNow(new Date(usageData.plan.periodEnd), {
                addSuffix: true,
              })}
            </span>
          </div>
        </div>
      </div>

      {/* Usage Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {usageItems.map(item => (
          <UsageCard key={item.title} {...item} />
        ))}
      </div>
    </div>
  )
}
