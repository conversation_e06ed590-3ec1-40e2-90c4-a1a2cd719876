'use client'
import React from 'react'
import { CreditCard, Package, ChartColumn } from 'lucide-react'
import { PlansTab, SubscriptionTab, UsageTab } from './_components'

// Custom tab button component matching settings page style
const TabButton = ({
  active,
  onClick,
  icon,
  label,
}: {
  active: boolean
  onClick: () => void
  icon: React.ReactNode
  label: string
}) => (
  <button
    className={`py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-colors ${
      active
        ? 'text-primary border-b-2 border-primary'
        : 'text-muted-foreground hover:text-foreground hover:bg-muted/40'
    }`}
    onClick={onClick}
  >
    {icon}
    <span className='hidden sm:inline'>{label}</span>
    <span className='sm:hidden'>{label.split(' ')[0]}</span>
  </button>
)

export default function BillingPage() {
  const [activeTab, setActiveTab] = React.useState('plans')

  return (
    <div className='container mx-auto p-6'>
      <div className='space-y-6'>
        {/* Page Header */}
        <div>
          <h1 className='text-2xl font-bold'>Billing</h1>
          <p className='text-muted-foreground'>
            Manage your subscription and billing preferences
          </p>
        </div>

        {/* Tabbed Interface */}
        <div className='bg-card rounded-lg overflow-hidden'>
          {/* Tab Navigation */}
          <div className='border-b border-border flex pl-4'>
            <div className='flex'>
              <TabButton
                active={activeTab === 'plans'}
                onClick={() => setActiveTab('plans')}
                icon={<Package className='h-4 w-4' />}
                label='Plans'
              />
              <TabButton
                active={activeTab === 'subscription'}
                onClick={() => setActiveTab('subscription')}
                icon={<CreditCard className='h-4 w-4' />}
                label='Subscription'
              />
              <TabButton
                active={activeTab === 'usage'}
                onClick={() => setActiveTab('usage')}
                icon={<ChartColumn className='h-4 w-4' />}
                label='Usage'
              />
            </div>
          </div>

          {/* Tab Content */}
          <div className='p-6'>
            {/* Plans Tab */}
            {activeTab === 'plans' && <PlansTab />}

            {/* Subscription Tab */}
            {activeTab === 'subscription' && <SubscriptionTab />}

            {/* Usage Tab */}
            {activeTab === 'usage' && <UsageTab />}
          </div>
        </div>
      </div>
    </div>
  )
}
