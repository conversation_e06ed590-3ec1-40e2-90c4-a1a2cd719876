import { NextResponse } from 'next/server'
import { getUserSession } from '@/lib/user-utils'
import { checkUsageLimit, incrementUsage } from '@/lib/usage-utils'

export async function GET() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id

    // Check current usage for all types
    const [projectsCheck, videoExportsCheck, aiImagesCheck] = await Promise.all(
      [
        checkUsageLimit(userId, 'projects'),
        checkUsageLimit(userId, 'videoExports'),
        checkUsageLimit(userId, 'aiImages'),
      ]
    )

    return NextResponse.json({
      userId,
      usage: {
        projects: projectsCheck,
        videoExports: videoExportsCheck,
        aiImages: aiImagesCheck,
      },
    })
  } catch (error) {
    console.error('Test usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id

    // Test incrementing usage for projects
    const result = await incrementUsage(userId, 'projects', 1)

    return NextResponse.json({
      success: result.success,
      message: 'Test usage increment completed',
    })
  } catch (error) {
    console.error('Test usage increment error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
