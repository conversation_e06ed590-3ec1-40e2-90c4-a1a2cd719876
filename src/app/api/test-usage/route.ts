import { NextResponse } from 'next/server'
import { getUserSession } from '@/lib/user-utils'
import {
  checkUsageLimit,
  incrementUsage,
  resetUsageForSubscription,
} from '@/lib/usage-utils'
import { createSubscriptionWelcomeEmailTemplate } from '@/lib/email/templates'
import { sendEmail } from '@/lib/email'
import { plans } from '@/config/constants'

export async function GET() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Check current usage for all types
    const [projectsCheck, videoExportsCheck, aiImagesCheck, storageCheck] =
      await Promise.all([
        checkUsageLimit(referenceId, 'projects'),
        checkUsageLimit(referenceId, 'videoExports'),
        checkUsageLimit(referenceId, 'aiImages'),
        checkUsageLimit(referenceId, 'storage'),
      ])

    return NextResponse.json({
      userId,
      referenceId,
      usage: {
        projects: projectsCheck,
        videoExports: videoExportsCheck,
        aiImages: aiImagesCheck,
        storage: storageCheck,
      },
    })
  } catch (error) {
    console.error('Test usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Test incrementing usage for projects and storage
    const [projectsResult, storageResult] = await Promise.all([
      incrementUsage(referenceId, 'projects', 1),
      incrementUsage(referenceId, 'storage', 1024 * 1024), // 1MB test
    ])

    return NextResponse.json({
      success: projectsResult.success && storageResult.success,
      message: 'Test usage increment completed',
      referenceId,
      results: {
        projects: projectsResult,
        storage: storageResult,
      },
    })
  } catch (error) {
    console.error('Test usage increment error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Test endpoint for usage reset functionality
export async function PUT() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Test resetting usage for subscription
    const result = await resetUsageForSubscription(
      userId,
      referenceId,
      'basic' // Test with basic plan
    )

    if (result.success) {
      // Check usage after reset
      const [projectsCheck, videoExportsCheck, aiImagesCheck, storageCheck] =
        await Promise.all([
          checkUsageLimit(referenceId, 'projects'),
          checkUsageLimit(referenceId, 'videoExports'),
          checkUsageLimit(referenceId, 'aiImages'),
          checkUsageLimit(referenceId, 'storage'),
        ])

      return NextResponse.json({
        success: true,
        message: 'Usage reset test completed',
        resetResult: result,
        referenceId,
        usageAfterReset: {
          projects: projectsCheck,
          videoExports: videoExportsCheck,
          aiImages: aiImagesCheck,
          storage: storageCheck,
        },
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Usage reset test failed',
        error: result.error,
        referenceId,
      })
    }
  } catch (error) {
    console.error('Test usage reset error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Test endpoint for subscription welcome email
export async function PATCH() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // const userId = session.user.id

    // // Get the user's active organization
    // const { getActiveOrganizationForUser } = await import(
    //   '@/lib/organization-utils'
    // )
    // const activeOrg = await getActiveOrganizationForUser(userId)
    // const organizationId = activeOrg?.organizationId || userId

    // Test sending subscription welcome email
    const testPlan = plans.find(p => p.name === 'basic')
    if (!testPlan) {
      return NextResponse.json(
        { error: 'Test plan not found' },
        { status: 500 }
      )
    }

    const { html, text } = createSubscriptionWelcomeEmailTemplate({
      userName: session.user.name,
      planName: testPlan.name,
      planFeatures: testPlan.features,
      planLimits: testPlan.limits,
    })

    await sendEmail({
      to: session.user.email,
      subject: `Test: Welcome to ${testPlan.name} Plan! 🎉 - Adori AI`,
      text,
      html,
    })

    return NextResponse.json({
      success: true,
      message: 'Test subscription welcome email sent successfully',
      sentTo: session.user.email,
      plan: testPlan.name,
    })
  } catch (error) {
    console.error('Test subscription email error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
