import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'
import { getUserSession } from '@/lib/user-utils'
import { incrementUsage } from '@/lib/usage-utils'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Define allowed file types and their configurations
const ALLOWED_FILE_TYPES = {
  'application/pdf': {
    bucket: 'pdfs',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'application/pdf',
    label: 'PDF',
  },
  'audio/mpeg': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/mpeg',
    label: 'Audio',
  },
  'audio/wav': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/wav',
    label: 'Audio',
  },
  'audio/flac': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/flac',
    label: 'Audio',
  },
  'audio/aac': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/aac',
    label: 'Audio',
  },
  'audio/ogg': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/ogg',
    label: 'Audio',
  },
  'audio/mp4': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/mp4',
    label: 'Audio',
  },
} as const

export async function POST(request: NextRequest) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId
    const referenceId = activeOrganizationId || userId

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check if file type is allowed
    const fileConfig =
      ALLOWED_FILE_TYPES[file.type as keyof typeof ALLOWED_FILE_TYPES]
    if (!fileConfig) {
      return NextResponse.json(
        {
          error: `Invalid file type. Allowed types: ${Object.keys(
            ALLOWED_FILE_TYPES
          )
            .map(
              type =>
                ALLOWED_FILE_TYPES[type as keyof typeof ALLOWED_FILE_TYPES]
                  .label
            )
            .join(', ')}`,
        },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > fileConfig.maxSize) {
      const maxSizeMB = fileConfig.maxSize / (1024 * 1024)
      return NextResponse.json(
        { error: `File too large. Maximum size is ${maxSizeMB}MB.` },
        { status: 400 }
      )
    }

    const filePath = `${userId}/${Date.now()}-${file.name}`

    const { error } = await supabase.storage
      .from(fileConfig.bucket)
      .upload(filePath, file, {
        upsert: false,
        contentType: fileConfig.contentType,
      })

    if (error) {
      console.error('Supabase upload error:', error)
      return NextResponse.json(
        { error: 'Failed to upload file' },
        { status: 500 }
      )
    }

    // Update storage usage for the organization (non-blocking)
    incrementUsage(referenceId, 'storage', file.size).catch(error => {
      console.warn('Failed to track storage usage:', error)
    })

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(fileConfig.bucket)
      .getPublicUrl(filePath)

    return NextResponse.json({
      success: true,
      url: urlData.publicUrl,
      path: filePath,
      filename: file.name,
      size: file.size,
      type: file.type,
      bucket: fileConfig.bucket,
    })
  } catch (error) {
    console.error('File upload error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
