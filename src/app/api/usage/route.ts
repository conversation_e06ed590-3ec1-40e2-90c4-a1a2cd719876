import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { usage } from '@/db/schema'
import { eq, sql } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'
import { plans } from '@/config/constants'

// Helper function to get plan limits
function getPlanLimits(planType: string) {
  const plan = plans.find(p => p.name === planType.toLowerCase())
  return plan?.limits || plans[0].limits // Default to free plan limits
}

export async function GET() {
  try {
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Get usage record for the organization or user
    let usageRecord = await db
      .select()
      .from(usage)
      .where(eq(usage.organizationId, referenceId))
      .limit(1)

    if (!usageRecord || usageRecord.length === 0) {
      // Create new usage record with default values
      const [newUsageRecord] = await db
        .insert(usage)
        .values({
          organizationId: referenceId,
          members: [userId], // Start with the current user
          planType: 'FREE',
          planStatus: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        })
        .returning()

      usageRecord = [newUsageRecord]
    } else {
      // Check if the current user is in the members array, if not add them
      const currentRecord = usageRecord[0]
      if (!currentRecord.members.includes(userId)) {
        await db
          .update(usage)
          .set({
            members: sql`array_append(${usage.members}, ${userId})`,
            updatedAt: new Date(),
          })
          .where(eq(usage.organizationId, referenceId))
      }
    }

    const currentUsage = usageRecord[0]
    const planLimits = getPlanLimits(currentUsage.planType)

    // Calculate usage percentages and format response
    const usageData = {
      plan: {
        type: currentUsage.planType,
        status: currentUsage.planStatus,
        periodStart: currentUsage.currentPeriodStart,
        periodEnd: currentUsage.currentPeriodEnd,
      },
      limits: planLimits,
      usage: {
        projects: {
          used: currentUsage.projectsUsed || 0,
          max: planLimits.projects,
          percentage:
            planLimits.projects > 0
              ? Math.round(
                  ((currentUsage.projectsUsed || 0) / planLimits.projects) * 100
                )
              : 0,
        },
        videoExports: {
          used: currentUsage.videoExportsUsed || 0,
          max: planLimits.videoExports,
          percentage:
            planLimits.videoExports > 0
              ? Math.round(
                  ((currentUsage.videoExportsUsed || 0) /
                    planLimits.videoExports) *
                    100
                )
              : 0,
        },
        aiImages: {
          used: currentUsage.aiImagesUsed || 0,
          max: planLimits.aiImages,
          percentage:
            planLimits.aiImages > 0
              ? Math.round(
                  ((currentUsage.aiImagesUsed || 0) / planLimits.aiImages) * 100
                )
              : 0,
        },
        teamMembers: {
          used: currentUsage.teamMembersUsed || 0,
          max: planLimits.teamMembers,
          percentage:
            planLimits.teamMembers > 0
              ? Math.round(
                  ((currentUsage.teamMembersUsed || 0) /
                    planLimits.teamMembers) *
                    100
                )
              : 0,
        },
        storage: {
          used: currentUsage.storageUsedBytes || 0,
          max: planLimits.storage,
          percentage:
            planLimits.storage > 0
              ? Math.round(
                  ((currentUsage.storageUsedBytes || 0) / planLimits.storage) *
                    100
                )
              : 0,
        },
      },
    }

    return NextResponse.json(usageData)
  } catch (error) {
    console.error('Usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
