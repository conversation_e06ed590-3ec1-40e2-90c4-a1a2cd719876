'use client'

import { useEffect, useState } from 'react'
import { Mail, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { authClient } from '@/lib/auth-client'

interface VerificationEmailModalProps {
  isOpen: boolean
  onClose: () => void
  email: string
}

export function VerificationEmailModal({
  isOpen,
  onClose,
  email,
}: VerificationEmailModalProps) {
  const [timeLeft, setTimeLeft] = useState(60)
  const [isResending, setIsResending] = useState(false)
  const [resendAttempts, setResendAttempts] = useState(0)
  const MAX_RESEND_ATTEMPTS = 3

  useEffect(() => {
    if (!isOpen) {
      setTimeLeft(60)
      setResendAttempts(0)
      return
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isOpen])

  const handleResendEmail = async () => {
    if (resendAttempts >= MAX_RESEND_ATTEMPTS) {
      toast.error(
        'Maximum resend attempts reached. Please contact support for assistance.'
      )
      return
    }

    setIsResending(true)
    try {
      await authClient.sendVerificationEmail({
        email: email,
        callbackURL: '/',
      })
      toast.success('Verification email sent successfully!')
      setResendAttempts(prev => prev + 1)
      setTimeLeft(60) // Reset timer
    } catch {
      toast.error('Failed to send verification email. Please try again.')
    } finally {
      setIsResending(false)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader className='text-center'>
          <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-green-500'>
            <Mail className='h-8 w-8 text-white' />
          </div>
          <DialogTitle className='text-2xl font-bold flex items-center justify-center'>
            You&apos;ve got mail!
          </DialogTitle>
          <DialogDescription className='text-base text-gray-500'>
            A confirmation link to activate your account has been sent to{' '}
            <span className='font-semibold text-gray-400'>{email}</span>. Make
            sure to double check the spam/junk folder.
          </DialogDescription>
        </DialogHeader>

        <div className='flex flex-col items-center gap-4'>
          <div className='text-center'>
            <p className='text-sm text-gray-500'>
              Didn&apos;t receive the email? You can resend it in:
            </p>
            <p className='text-2xl font-mono font-bold text-blue-600'>
              {formatTime(timeLeft)}
            </p>
          </div>

          <Button
            onClick={handleResendEmail}
            disabled={
              timeLeft > 0 ||
              isResending ||
              resendAttempts >= MAX_RESEND_ATTEMPTS
            }
            className='w-full'
            variant='outline'
          >
            {isResending ? (
              <>
                <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
                Sending...
              </>
            ) : resendAttempts >= MAX_RESEND_ATTEMPTS ? (
              <>
                <Mail className='mr-2 h-4 w-4' />
                Max attempts reached
              </>
            ) : (
              <>
                <Mail className='mr-2 h-4 w-4' />
                Resend email ({MAX_RESEND_ATTEMPTS - resendAttempts} left)
              </>
            )}
          </Button>

          <div className='text-center pt-4  '>
            <p className='text-sm text-gray-500'>
              Do you need help?{' '}
              <a
                href='mailto:<EMAIL>'
                className='text-blue-600 hover:text-blue-700 underline'
              >
                Contact support
              </a>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
