'use client'

import { Building2, Check, ChevronsUpDown, User, UserPlus } from 'lucide-react'
// import { useUser, useClerk } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import {
  getDisplayName,
  isFreeEmail,
  generateOrganizationSlug,
  getOrganizationLogo,
} from '@/lib/email-utils'
import { useCompanyAvatar } from '@/hooks/use-company-avatar'
import { useEffect } from 'react'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

export function NavOrg() {
  const { isMobile, state } = useSidebar()
  const isCollapsed = state === 'collapsed'
  // const { user } = useUser()
  // const { signOut } = useClerk()
  const router = useRouter()
  const { data: session } = authClient.useSession()

  // Get all organizations from API
  const { data: organizations, isPending: organizationsLoading } =
    authClient.useListOrganizations()

  // Get active organization
  const { data: activeOrganization } = authClient.useActiveOrganization()

  // Create organization if user has none
  useEffect(() => {
    const createOrganizationIfNeeded = async () => {
      if (
        !organizationsLoading &&
        organizations &&
        organizations.length === 0 &&
        session?.user
      ) {
        try {
          const orgName = getDisplayName(
            session.user.name || '',
            session.user.email || ''
          )
          const orgSlug = generateOrganizationSlug(
            session.user.name || '',
            session.user.email || ''
          )
          const orgLogo = getOrganizationLogo(
            session.user.name || '',
            session.user.email || ''
          )

          await authClient.organization.create({
            name: orgName,
            slug: orgSlug,
            logo: orgLogo,
            userId: session.user.id,
            keepCurrentActiveOrganization: true,
          })

          // toast.success('Workspace created successfully')
        } catch (error) {
          console.error('Failed to create organization:', error)
          toast.error('Failed to create workspace')
        }
      }
    }

    createOrganizationIfNeeded()
  }, [organizations, organizationsLoading, session?.user])

  // Get company avatar if it's a company email
  const { avatarUrl: companyAvatarUrl } = useCompanyAvatar(
    session?.user?.email || ''
  )

  // Determine workspace name: API first, then fallback to hardcoded
  const getWorkspaceName = () => {
    // Try to get from active organization first
    if (activeOrganization?.name) {
      return activeOrganization.name
    }

    // Fallback to hardcoded workspace name generation
    return getDisplayName(session?.user?.name || '', session?.user?.email || '')
  }

  const workspaceName = getWorkspaceName()

  // Handle switching to a different organization
  const handleSwitchOrganization = async (organizationId: string) => {
    try {
      await authClient.organization.setActive({
        organizationId,
      })
      toast.success('Organization switched successfully')
    } catch (error) {
      console.error('Failed to switch organization:', error)
      toast.error('Failed to switch organization')
    }
  }

  if (!session?.user) {
    return (
      <SidebarMenu>
        <SidebarMenuItem className='flex justify-center'>
          <SidebarMenuButton
            size={isCollapsed ? 'sm' : 'lg'}
            tooltip='User'
            className='flex justify-center'
          >
            <Avatar className='h-8 w-8 rounded-lg'>
              <AvatarFallback className='rounded-lg'>
                <User className='size-4' />
              </AvatarFallback>
            </Avatar>
            <div className='flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden'>
              <span className='truncate font-semibold'>Guest</span>
              <div className='truncate text-xs'>Not signed in</div>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  const isFree = isFreeEmail(session.user.email || '')
  const firstName = session.user.name?.split(' ')[0] || 'U'

  // Determine avatar image source
  let avatarImageSrc = session.user.image || null
  if (!isFree && companyAvatarUrl) {
    avatarImageSrc = companyAvatarUrl
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem className='flex justify-center'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size={isCollapsed ? 'sm' : 'lg'}
              tooltip={workspaceName}
              className='flex justify-center data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
            >
              <Avatar className='h-8 w-8 rounded-lg'>
                <AvatarImage
                  src={avatarImageSrc || undefined}
                  alt={workspaceName}
                />
                <AvatarFallback className='rounded-lg'>
                  {firstName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className='flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden'>
                <span className='truncate font-semibold'>{workspaceName}</span>
              </div>
              <ChevronsUpDown className='ml-auto size-4 group-data-[collapsible=icon]:hidden' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
            side={isMobile ? 'bottom' : 'right'}
            align='end'
            sideOffset={4}
          >
            <DropdownMenuGroup>
              <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
                <Building2 className='size-4' />
                <span>Workspaces</span>
              </div>
              <DropdownMenuSeparator />

              {/* List all organizations */}
              {organizationsLoading ? (
                <DropdownMenuItem disabled>
                  <div className='flex items-center gap-2'>
                    <div className='h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                    <span>Loading workspaces...</span>
                  </div>
                </DropdownMenuItem>
              ) : organizations && organizations.length > 0 ? (
                organizations.map(org => (
                  <DropdownMenuItem
                    key={org.id}
                    onClick={() => handleSwitchOrganization(org.id)}
                    className='flex items-center justify-between'
                  >
                    <div className='flex items-center gap-2'>
                      <Avatar className='h-6 w-6 rounded-lg'>
                        <AvatarImage
                          src={org.logo || undefined}
                          alt={org.name}
                        />
                        <AvatarFallback className='rounded-lg text-xs'>
                          {org.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className='truncate'>{org.name}</span>
                    </div>
                    {activeOrganization?.id === org.id && (
                      <Check className='h-4 w-4 text-green-500' />
                    )}
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem disabled>
                  <span>No workspaces found</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuGroup>

            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={() => {
                  router.push('/settings/workspace')
                }}
              >
                <UserPlus className='mr-2 size-4' />
                Invite team members
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
