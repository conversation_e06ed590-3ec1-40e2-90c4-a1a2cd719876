'use client'

import * as React from 'react'
import {
  ChevronDown,
  Video,
  Image as ImageIcon,
  ChartColumn,
  FolderOpen,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  SidebarMenuButton,
  SidebarMenu,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { UpgradeButton } from '@/components/upgrade-button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { useUsage, type UsageData } from '@/hooks/use-usage'
import { useCachedActiveSubscription } from '@/hooks/useSubscription'
import { plans } from '@/config/constants'

export interface UsageMetric {
  id: string
  name: string
  max: number
  used: number
  icon: React.ReactNode
  color: string
}

interface UsageMetricsProps {
  className?: string
  isCollapsed?: boolean
}

// Helper function to create usage metrics from API data
const createUsageMetrics = (
  usageData: UsageData | undefined,
  subscription: { plan?: string } | null = null
): UsageMetric[] => {
  if (!usageData || !usageData.usage) return []

  // Determine the actual plan type
  const planType =
    subscription?.plan?.toLowerCase() ||
    usageData.plan?.type?.toLowerCase() ||
    'free'
  const plan = plans.find((p: { name: string }) => p.name === planType)
  const limits = plan?.limits || plans[0].limits // Default to free plan

  return [
    {
      id: 'projects',
      name: 'Projects',
      max: limits.projects,
      used: usageData.usage.projects.used,
      icon: <FolderOpen className='h-4 w-4' />,
      color: 'from-blue-500 to-blue-600',
    },

    {
      id: 'images',
      name: 'AI Images',
      max: limits.aiImages,
      used: usageData.usage.aiImages.used,
      icon: <ImageIcon className='h-4 w-4' />,
      color: 'from-purple-500 to-purple-600',
    },
    {
      id: 'exports',
      name: 'Video Exports',
      max: limits.videoExports,
      used: usageData.usage.videoExports.used,
      icon: <Video className='h-4 w-4' />,
      color: 'from-green-500 to-green-600',
    },
  ]
}

export function UsageMetrics({ className, isCollapsed }: UsageMetricsProps) {
  const { data: usageData, isLoading: usageLoading } = useUsage()
  const { subscription, isLoading: subscriptionLoading } =
    useCachedActiveSubscription()
  const isLoading = usageLoading || subscriptionLoading

  // Use subscription data to override plan type if available
  const effectiveUsageData = usageData
    ? {
        ...usageData,
        plan: {
          ...usageData.plan,
          type: subscription?.plan?.toUpperCase() || usageData.plan.type,
        },
      }
    : usageData

  const metrics = createUsageMetrics(effectiveUsageData, subscription)
  const [selectedMetric, setSelectedMetric] =
    React.useState<UsageMetric | null>(null)
  const [showAll] = React.useState(true)
  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false)

  // Set selected metric when data loads
  React.useEffect(() => {
    if (metrics.length > 0 && !selectedMetric) {
      setSelectedMetric(metrics[0])
    }
  }, [metrics, selectedMetric])

  // Calculate usage percentage
  const getUsagePercentage = (used: number, max: number) => {
    return Math.min((used / max) * 100, 100)
  }

  if (isCollapsed) {
    return (
      <SidebarMenu className={className}>
        <SidebarMenuItem>
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                tooltip='Usage Overview'
                className='flex justify-center'
              >
                <ChartColumn className='size-4' />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='start' side='right' className='w-72'>
              <DropdownMenuLabel className='flex items-center gap-2'>
                <ChartColumn className='h-4 w-4' />
                Usage Overview
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className='p-3 space-y-4'>
                {isLoading ? (
                  <div className='space-y-2'>
                    <div className='h-4 bg-muted rounded animate-pulse' />
                    <div className='h-2 bg-muted rounded animate-pulse' />
                  </div>
                ) : (
                  metrics.map((metric: UsageMetric) => (
                    <div key={metric.id} className='space-y-2'>
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-2'>
                          {metric.icon}
                          <span className='text-sm font-medium'>
                            {metric.name}
                          </span>
                        </div>
                        <Badge
                          variant={
                            getUsagePercentage(metric.used, metric.max) >= 90
                              ? 'destructive'
                              : getUsagePercentage(metric.used, metric.max) >=
                                  70
                                ? 'secondary'
                                : 'default'
                          }
                          className='text-xs px-2 py-0.5'
                        >
                          {metric.used}/{metric.max}
                        </Badge>
                      </div>
                      <Progress
                        value={getUsagePercentage(metric.used, metric.max)}
                        className='h-2'
                      />
                    </div>
                  ))
                )}
              </div>
              <DropdownMenuSeparator />
              <div className='p-3'>
                <UpgradeButton className='w-full' size='sm' />
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return (
    <div className={cn('px-3 py-4 border-t', className)}>
      {/* Header with toggle */}
      <div className='flex items-center justify-between mb-4'>
        <div className='flex items-center gap-2'>
          <ChartColumn className='h-4 w-4 text-muted-foreground' />
          <h4 className='font-semibold text-sm'>Usage</h4>
        </div>
        <Badge variant='outline' className='text-xs px-2 py-0.5'>
          {effectiveUsageData?.plan?.type?.toLowerCase() === 'free'
            ? 'Free'
            : effectiveUsageData?.plan?.type?.toLowerCase() === 'basic'
              ? 'Basic'
              : effectiveUsageData?.plan?.type?.toLowerCase() === 'premium'
                ? 'Premium'
                : effectiveUsageData?.plan?.type || 'Free'}
        </Badge>
      </div>

      {isLoading ? (
        /* Loading state */
        <div className='space-y-4 mb-4'>
          {[1, 2, 3].map(i => (
            <div key={i} className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <div className='h-4 w-4 bg-muted rounded animate-pulse' />
                  <div className='h-4 w-16 bg-muted rounded animate-pulse' />
                </div>
                <div className='h-5 w-12 bg-muted rounded animate-pulse' />
              </div>
              <div className='h-2 bg-muted rounded animate-pulse' />
            </div>
          ))}
        </div>
      ) : showAll ? (
        /* Show all metrics */
        <div className='space-y-4 mb-4'>
          {metrics.map((metric: UsageMetric) => (
            <div key={metric.id} className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  {metric.icon}
                  <span className='text-sm font-medium'>{metric.name}</span>
                </div>
                <Badge
                  variant={
                    getUsagePercentage(metric.used, metric.max) >= 90
                      ? 'destructive'
                      : getUsagePercentage(metric.used, metric.max) >= 70
                        ? 'secondary'
                        : 'default'
                  }
                  className='text-xs px-2 py-0.5'
                >
                  {metric.used}/{metric.max}
                </Badge>
              </div>
              <Progress
                value={getUsagePercentage(metric.used, metric.max)}
                className='h-2'
              />
            </div>
          ))}
        </div>
      ) : (
        /* Show single metric with dropdown */
        <div className='space-y-3 mb-4'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='ghost'
                className='w-full justify-between p-2 h-auto hover:bg-accent/50 rounded-md border-0 transition-colors'
              >
                <div className='flex items-center gap-3 text-left'>
                  <div className='p-1.5 rounded-md bg-muted/50'>
                    {selectedMetric?.icon}
                  </div>
                  <div className='flex-1'>
                    <div className='text-sm font-medium'>
                      {selectedMetric?.name}
                    </div>
                    <div className='text-xs text-muted-foreground'>
                      {selectedMetric?.used} of {selectedMetric?.max} used
                    </div>
                  </div>
                </div>
                <ChevronDown className='h-4 w-4 text-muted-foreground' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='w-56'>
              {metrics.map((metric: UsageMetric) => (
                <DropdownMenuItem
                  key={metric.id}
                  onClick={() => setSelectedMetric(metric)}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-2'>
                    {metric.icon}
                    <span>{metric.name}</span>
                  </div>
                  <Badge variant='outline' className='text-xs'>
                    {metric.used}/{metric.max}
                  </Badge>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Progress bar for selected metric - no bottom numbers */}
          <div className='space-y-2'>
            <Progress
              value={getUsagePercentage(
                selectedMetric?.used || 0,
                selectedMetric?.max || 1
              )}
              className='h-2'
            />
          </div>
        </div>
      )}

      {/* Upgrade button */}
      <UpgradeButton className='w-full' size='sm' />
    </div>
  )
}
