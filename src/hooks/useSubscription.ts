import { useState, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'
import { authClient } from '@/lib/auth-client'
import { toast } from 'sonner'

export interface SubscriptionData {
  id: string
  status: string
  plan: string
  referenceId: string
  limits?: Record<string, number>
  periodStart?: string | Date
  periodEnd?: string | Date
  cancelAtPeriodEnd?: boolean
}

export function useSubscription() {
  const [isLoading, setIsLoading] = useState(false)
  const { data: session } = authClient.useSession()
  // const { data: activeOrg } = authClient.useActiveOrganization()

  const upgradeSubscription = async ({
    plan,
    annual = false,
    successUrl = '/billing',
    cancelUrl = '/billing',
  }: {
    plan: 'basic' | 'premium'
    annual?: boolean
    successUrl?: string
    cancelUrl?: string
  }) => {
    setIsLoading(true)

    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      if (!referenceId) {
        toast.error('No valid reference ID found for subscription upgrade')
        return { error: new Error('No valid reference ID') }
      }

      const { data, error } = await authClient.subscription.upgrade({
        plan,
        annual,
        referenceId,
        successUrl,
        cancelUrl,
        disableRedirect: false, // Allow redirect to Stripe checkout
      })

      if (error) {
        toast.error(error.message || 'Failed to upgrade subscription')
        return { error }
      }

      toast.success('Redirecting to checkout...')
      return { data }
    } catch (error) {
      console.error('Subscription upgrade error:', error)
      toast.error('An unexpected error occurred')
      return { error: error as Error }
    } finally {
      setIsLoading(false)
    }
  }

  const cancelSubscription = async ({
    subscriptionId,
    returnUrl = '/billing',
  }: {
    subscriptionId: string
    returnUrl?: string
  }) => {
    setIsLoading(true)

    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      if (!referenceId) {
        toast.error('No valid reference ID found for subscription cancellation')
        return { error: new Error('No valid reference ID') }
      }

      const { data, error } = await authClient.subscription.cancel({
        referenceId,
        subscriptionId,
        returnUrl,
      })

      if (error) {
        toast.error(error.message || 'Failed to cancel subscription')
        return { error }
      }

      toast.success('Redirecting to billing portal...')
      return { data }
    } catch (error) {
      console.error('Subscription cancellation error:', error)
      toast.error('An unexpected error occurred')
      return { error: error as Error }
    } finally {
      setIsLoading(false)
    }
  }

  const restoreSubscription = async ({
    subscriptionId,
  }: {
    subscriptionId: string
  }) => {
    setIsLoading(true)

    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      if (!referenceId) {
        toast.error('No valid reference ID found for subscription restoration')
        return { error: new Error('No valid reference ID') }
      }

      const { data, error } = await authClient.subscription.restore({
        referenceId,
        subscriptionId,
      })

      if (error) {
        toast.error(error.message || 'Failed to restore subscription')
        return { error }
      }

      toast.success('Subscription restored successfully')
      return { data }
    } catch (error) {
      console.error('Subscription restoration error:', error)
      toast.error('An unexpected error occurred')
      return { error: error as Error }
    } finally {
      setIsLoading(false)
    }
  }

  const getSubscriptions = useCallback(async () => {
    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      // Only proceed if we have a valid referenceId
      if (!referenceId) {
        console.error('No valid referenceId found for subscription query')
        return { subscriptions: [], error: new Error('No valid reference ID') }
      }

      const { data: subscriptions, error } = await authClient.subscription.list(
        {
          query: {
            referenceId,
          },
        }
      )

      if (error) {
        console.error('Failed to fetch subscriptions:', error)
        return { subscriptions: [], error }
      }

      return { subscriptions: subscriptions || [], error: null }
    } catch (error) {
      console.error('Subscription fetch error:', error)
      return { subscriptions: [], error: error as Error }
    }
  }, [session?.session?.activeOrganizationId, session?.user?.id])

  const getActiveSubscription =
    useCallback(async (): Promise<SubscriptionData | null> => {
      const { subscriptions, error } = await getSubscriptions()

      if (error || !subscriptions.length) {
        return null
      }

      // Find active or trialing subscription
      const activeSubscription = subscriptions.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      )

      return (activeSubscription as SubscriptionData) || null
    }, [getSubscriptions])

  return {
    isLoading,
    upgradeSubscription,
    cancelSubscription,
    restoreSubscription,
    getSubscriptions,
    getActiveSubscription,
    // activeOrg,
  }
}

// Cached hook for getting subscriptions
export function useCachedSubscriptions() {
  const { data: session } = authClient.useSession()
  const referenceId =
    session?.session?.activeOrganizationId || session?.user?.id

  return useQuery({
    queryKey: ['subscriptions', referenceId],
    queryFn: async () => {
      const { data: subscriptions, error } = await authClient.subscription.list(
        {
          query: {
            referenceId,
          },
        }
      )

      if (error) {
        throw new Error(error.message || 'Failed to fetch subscriptions')
      }

      return subscriptions || []
    },
    enabled: !!referenceId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })
}

// Cached hook for getting active subscription
export function useCachedActiveSubscription() {
  const {
    data: subscriptions,
    isLoading,
    error,
    refetch,
  } = useCachedSubscriptions()

  const activeSubscription = subscriptions?.find(
    (sub: SubscriptionData) =>
      sub.status === 'active' || sub.status === 'trialing'
  )

  return {
    subscription: activeSubscription || null,
    isLoading,
    error,
    refetch,
  }
}
