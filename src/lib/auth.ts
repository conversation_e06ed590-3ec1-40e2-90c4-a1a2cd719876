import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { nextCookies } from 'better-auth/next-js'
import { organization } from 'better-auth/plugins'
import { headers } from 'next/headers'
import { emailHarmony } from 'better-auth-harmony'
import { stripe } from '@better-auth/stripe'
import Stripe from 'stripe'
import { eq } from 'drizzle-orm'

// import ForgotPasswordEmail from '@/components/emails/forgot-password'

import { db } from '@/lib/db'
import { schema } from '@/db/schema'
import {
  sendEmail,
  createVerificationEmailTemplate,
  createPasswordResetEmailTemplate,
  createWelcomeEmailTemplate,
  createOrganizationInvitationTemplate,
} from '@/lib/email'
import { getActiveOrganizationForUser } from '@/lib/organization-utils'
import { plans } from '@/config/constants'

const stripeClient = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil',
})

export const auth = betterAuth({
  emailVerification: {
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      const { html, text } = createVerificationEmailTemplate(url, user.name)
      await sendEmail({
        to: user.email,
        subject: 'Verify your email address - Adori AI',
        text,
        html,
      })
    },
    afterEmailVerification: async (user, request) => {
      //Create a new organization for the user
      try {
        console.log(`Organization check: ${user.name}`)
        console.log(`Organization check: ${user.id}`)
        console.log('Organization check:', await request?.headers)
        console.log('Organization check:', await headers())

        // Note: Organization creation is handled by the organization plugin
        console.log('User verified, organization will be created by plugin')
        console.log('Organization creation completed')
      } catch (error) {
        console.error('Failed to create organization:', error)
      }
      // Send welcome email after successful email verification
      try {
        const { html, text } = createWelcomeEmailTemplate(user.name)
        await sendEmail({
          to: user.email,
          subject: 'Welcome to Adori AI! 🎉',
          text,
          html,
        })
        console.log(`Welcome email sent to: ${user.email}`)
      } catch (error) {
        console.error('Failed to send welcome email:', error)
        // Don't throw error to avoid breaking the verification flow
      }
    },
  },

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendResetPassword: async ({ user, url }) => {
      const { html, text } = createPasswordResetEmailTemplate(url, user.name)
      await sendEmail({
        to: user.email,
        subject: 'Reset your password - Adori AI',
        text,
        html,
      })
    },
    onPasswordReset: async ({ user }) => {
      // Log password reset for security monitoring
      console.log(`Password reset completed for user: ${user.email}`)
    },
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_OAUTH_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_OAUTH_CLIENT_SECRET as string,
    },
  },
  database: drizzleAdapter(db, {
    provider: 'pg',
    schema: schema,
  }),
  databaseHooks: {
    user: {
      create: {
        before: async user => {
          // Debug: Log the user being created
          console.log('Creating user with email:', user.email)
          console.log('User data:', user)
        },
        after: async user => {
          // Debug: Log the created user
          console.log('Created user:', user)

          // Create organization for the new user
          try {
            const {
              getDisplayName,
              getOrganizationLogo,
              generateOrganizationSlug,
            } = await import('@/lib/email-utils')

            const orgName = getDisplayName(user.name || '', user.email)
            const orgSlug = generateOrganizationSlug(
              user.name || '',
              user.email
            )
            const orgLogo = getOrganizationLogo(user.name || '', user.email)

            // Create organization using Better Auth API
            const organization = await auth.api.createOrganization({
              body: {
                name: orgName,
                slug: orgSlug,
                logo: orgLogo,
                userId: user.id,
                keepCurrentActiveOrganization: true,
              },
              headers: await headers(),
            })

            console.log(
              `Organization "${orgName}" created for user ${user.email}`
            )

            // Set the newly created organization as active
            if (organization) {
              await auth.api.setActiveOrganization({
                body: {
                  organizationId: organization.id,
                  organizationSlug: organization.slug,
                },
                headers: await headers(),
              })

              console.log(
                `Set "${orgName}" as active organization for user ${user.email}`
              )
            }
          } catch (error) {
            console.error('Failed to create organization for new user:', error)
            // Don't throw error to avoid breaking user creation
          }
        },
      },
    },
    session: {
      create: {
        before: async session => {
          const organization = await getActiveOrganizationForUser(
            session.userId
          )
          return {
            data: {
              ...session,
              activeOrganizationId: organization?.organizationId || null,
            },
          }
        },
      },
    },
  },
  plugins: [
    stripe({
      stripeClient,
      stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
      createCustomerOnSignUp: true,
      onEvent: async event => {
        // Handle any Stripe event
        console.log(`Stripe webhook event: ${event.type}`)

        switch (event.type) {
          case 'invoice.paid':
            console.log('Invoice paid:', event.data.object)
            break
          case 'payment_intent.succeeded':
            console.log('Payment succeeded:', event.data.object)
            break
          case 'customer.subscription.trial_will_end':
            console.log('Trial ending soon:', event.data.object)
            break
        }
      },
      subscription: {
        enabled: true,
        plans: plans,
        authorizeReference: async ({ user, referenceId }) => {
          // If referenceId is 'user', allow it (user-level subscription)
          if (referenceId === 'user') {
            return true
          }

          // For organization-level subscriptions, check if user is a member
          try {
            const member = await db
              .select()
              .from(schema.member)
              .where(
                eq(schema.member.userId, user.id) &&
                  eq(schema.member.organizationId, referenceId)
              )
              .limit(1)

            if (member.length === 0) {
              return false
            }

            // Allow if user is owner or admin of the organization
            return member[0].role === 'owner' || member[0].role === 'admin'
          } catch (error) {
            console.error('Error checking organization membership:', error)
            return false
          }
        },
        getCheckoutSessionParams: () => {
          return {
            params: {
              allow_promotion_codes: true,
            },
          }
        },
        onSubscriptionComplete: async ({ subscription, plan }) => {
          // Called when a subscription is successfully created
          console.log(
            `Subscription completed: ${subscription.id} for plan ${plan.name}`
          )
          console.log(`Reference ID: ${subscription.referenceId}`)

          // You can add custom logic here like:
          // - Send welcome email
          // - Update user/organization limits
          // - Log subscription creation
        },
        onSubscriptionUpdate: async ({ subscription }) => {
          // Called when a subscription is updated
          console.log(`Subscription updated: ${subscription.id}`)
          console.log(`New status: ${subscription.status}`)
        },
        onSubscriptionCancel: async ({ subscription, cancellationDetails }) => {
          // Called when a subscription is canceled
          console.log(`Subscription canceled: ${subscription.id}`)
          console.log(`Cancellation details:`, cancellationDetails)

          // You can add custom logic here like:
          // - Send cancellation email
          // - Update user/organization limits
          // - Log subscription cancellation
        },
        onSubscriptionDeleted: async ({ subscription }) => {
          // Called when a subscription is deleted
          console.log(`Subscription deleted: ${subscription.id}`)
        },
      },
    }),
    emailHarmony({
      allowNormalizedSignin: true,
    }),
    organization({
      sendInvitationEmail: async data => {
        const inviteLink = `${process.env.NEXT_PUBLIC_APP_URL}/accept-invitation/${data.id}`
        const { html, text } = createOrganizationInvitationTemplate({
          email: data.email,
          invitedByUsername: data.inviter.user.name || data.inviter.user.email,
          invitedByEmail: data.inviter.user.email,
          teamName: data.organization.name,
          inviteLink,
        })
        await sendEmail({
          to: data.email,
          subject: `You've been invited to join ${data.organization.name} on Adori AI`,
          text,
          html,
        })
      },
      organizationCreation: {
        // beforeCreate: async ({ organization, user }) => {
        //   // Use our utility functions for consistent naming and slug generation
        //   const orgName = getDisplayName(user.name || '', user.email)
        //   const orgSlug = generateOrganizationSlug(user.name || '', user.email)
        //   const orgLogo = getOrganizationLogo(user.name || '', user.email)
        //   return {
        //     data: {
        //       ...organization,
        //       name: orgName,
        //       slug: orgSlug,
        //       logo: orgLogo,
        //     },
        //   }
        // },
        // afterCreate: async ({ organization, user }) => {
        //   console.log(
        //     `Organization "${organization.name}" created for user ${user.email}`
        //   )
        // },
      },
    }),
    nextCookies(), //Should be last
  ],
})
