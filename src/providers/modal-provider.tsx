'use client'

import { useEffect, useState } from 'react'
import { useModalStore } from '@/store/use-modal-store'
import { Modal } from '@/components/ui/modal'
import { PricingModal } from '@/components/modals/pricing-modal'
import { ModalConfigType, DEFAULT_MODAL_CONFIG } from '@/types/modal'

// Modal configuration
const MODAL_CONFIG: ModalConfigType = {
  pricing: {
    title: 'Subscription Plans',
    size: '7xl',
  },
  // Add more modal configurations as needed
}

/**
 * ModalProvider component
 *
 * This component handles the rendering of different modals based on the current modal state.
 * It uses the modal store to determine which modal to show and passes the appropriate props.
 */
export const ModalProvider = () => {
  const { type, isOpen, onClose, data } = useModalStore()
  const [isMounted, setIsMounted] = useState(false)

  // Map of modal types to their components
  const MODAL_COMPONENTS = {
    pricing: PricingModal,
    // Add more modal types here as needed
  }

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted || !type || !isOpen) return null

  const config =
    type in MODAL_CONFIG ? MODAL_CONFIG[type]! : DEFAULT_MODAL_CONFIG
  const ModalComponent = MODAL_COMPONENTS[type as keyof typeof MODAL_COMPONENTS]

  if (!ModalComponent) return null

  return (
    <Modal
      title={data.title || config.title}
      description={data.description}
      isOpen={isOpen}
      onClose={onClose}
      type={type}
      size={config.size}
    >
      <ModalComponent />
    </Modal>
  )
}
